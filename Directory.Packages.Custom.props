﻿    
<Project>
  <!-- 
    Custom Package Versions that supplement auto-generated Directory.Packages.props
    
    This file allows you to:
    1. Add custom packages not in the auto-generated file
    2. Override specific package versions from the auto-generated file
    3. Add development-only or conditional packages
    4. Define custom version variables for consistency
  -->
  
  <PropertyGroup>
    <!-- Custom version variables for consistency -->
    <!-- <MyCustomLibraryVersion>1.2.3</MyCustomLibraryVersion> -->
    <!-- <ExperimentalPackageVersion>2.0.0-beta1</ExperimentalPackageVersion> -->
  </PropertyGroup>

  <ItemGroup>
    <!-- 
    Add your custom packages here:
    
    Example: Custom packages not in the main Directory.Packages.props
    <PackageVersion Include="MyCustomLibrary" Version="$(MyCustomLibraryVersion)" />
    <PackageVersion Include="ExperimentalPackage" Version="$(ExperimentalPackageVersion)" />
    
    Example: Override specific package versions from the auto-generated file
    <PackageVersion Include="Newtonsoft.Json" Version="12.0.3" />
    
    Example: Development-only packages
    <PackageVersion Include="DevOnlyTool" Version="1.0.0" Condition="'$(Configuration)' == 'Debug'" />
    
    Example: Project-specific packages
    <PackageVersion Include="SpecialLibrary" Version="2.0.0" 
                    Condition="$(MSBuildProjectName.Contains('SpecialProject'))" />
    -->
  </ItemGroup>
</Project>
