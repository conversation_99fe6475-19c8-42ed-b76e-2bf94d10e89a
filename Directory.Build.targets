﻿
<Project>
  <!-- Auto-Publishing Target for Framework Projects -->
  <Target Name="AutoPublishToLocalFeed" AfterTargets="Pack">
    <PropertyGroup>
      <LocalNuGetFeed Condition="'$(LocalNuGetFeed)' == ''">C:\dev\nuget-local</LocalNuGetFeed>
    </PropertyGroup>
    
    <ItemGroup>
      <PackageFiles Include="$(MSBuildProjectDirectory)\bin\$(Configuration)\*.nupkg" />
      <SymbolPackageFiles Include="$(MSBuildProjectDirectory)\bin\$(Configuration)\*.snupkg" />
    </ItemGroup>
    
    <!-- Ensure local feed directory exists -->
    <MakeDir Directories="$(LocalNuGetFeed)" Condition="!Exists('$(LocalNuGetFeed)')" />
    
    <!-- Auto-publish Debug builds to local feed -->
    <Exec Command="dotnet nuget push &quot;%(PackageFiles.Identity)&quot;   --source &quot;$(LocalNuGetFeed)&quot; --skip-duplicate" 
          ContinueOnError="true" 
          Condition="'$(Configuration)' == 'Debug' AND @(PackageFiles->Count()) > 0" />
          
    <!-- Copy symbol packages directly to local feed (folder feeds don't support proper symbol package pushing) -->
    <Copy SourceFiles="@(SymbolPackageFiles)" 
          DestinationFolder="$(LocalNuGetFeed)" 
          SkipUnchangedFiles="true"
          ContinueOnError="true"
          Condition="'$(Configuration)' == 'Debug' AND @(SymbolPackageFiles->Count()) > 0" />
  </Target>
</Project> 
